from functools import lru_cache
from typing import List, Optional

from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Manages application settings with validation.
    Reads from a .env file and environment variables.
    """

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    # Core application settings
    BASE_URL: str = "http://localhost:8000"
    ENVIRONMENT: str = "development"
    PORT: int = 8000
    TESTING: bool = False

    # Database
    DATABASE_URL: str = "postgresql+asyncpg://app_user:dev_password@localhost:5432/ecommerce_db"
    TEST_DATABASE_URL: str = "postgresql+asyncpg://app_user:dev_password@localhost:5432/test_ecommerce_db"

    def model_post_init(self, __context):
        """Override DATABASE_URL with TEST_DATABASE_URL when testing."""
        if self.TESTING:
            self.DATABASE_URL = self.TEST_DATABASE_URL

    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 43200  # 30 days

    # CORS
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:5173"]

    # GCP
    GCP_PROJECT_ID: Optional[str] = None
    GCP_BUCKET_NAME: Optional[str] = None

    # Shopify App Configuration (global app settings)
    SHOPIFY_API_KEY: Optional[str] = None
    SHOPIFY_API_SECRET: Optional[str] = None
    SHOPIFY_WEBHOOK_SECRET: Optional[str] = None

    # Airbyte configuration
    AIRBYTE_API_URL: str = "http://localhost:8000"
    AIRBYTE_USER_ID: Optional[str] = None
    AIRBYTE_PASSWORD: Optional[str] = None
    AIRBYTE_WORKSPACE_ID: Optional[str] = None
    AIRBYTE_DESTINATION_NAME: Optional[str] = None

    # Airbyte Database Configuration (separate from main app DB)
    AIRBYTE_DATABASE_HOST: str = "db"
    AIRBYTE_DATABASE_PORT: int = 5432
    AIRBYTE_DATABASE_USER: str = "airbyte"
    AIRBYTE_DATABASE_PASSWORD: str = "airbyte"
    AIRBYTE_DATABASE_NAME: str = "airbyte"
    AIRBYTE_DATABASE_URL: str = "************************************/airbyte"

    # Sync configuration
    PER_SHOP_CONCURRENCY: int = 1
    SYNC_MAX_RETRIES: int = 3
    SYNC_BACKOFF_BASE: int = 30
    CONSUMER_BATCH_SIZE: int = 100

    # Video Generation
    VIDEO_PROVIDER: str = "mock"  # mock, revid_ai, vidify, product_studio
    VEO3_API_KEY: Optional[str] = None

    # Image Generation
    IMAGE_PROVIDER: str = "mock" # mock, banana, dall_e, midjourney
    BANANA_API_KEY: Optional[str] = None  # Banana for image generation

    # Text Generation
    TEXT_PROVIDER: str = "mock"  # mock, gemini, openai, claude
    GEMINI_API_KEY: Optional[str] = None  # Gemini for text generation

    # Redis (for job queue)
    REDIS_URL: str

    # Storage (S3/CloudFlare R2/GCP)
    STORAGE_PROVIDER: str = "local"  # s3, cloudflare_r2, google_cloud, local
    S3_BUCKET_NAME: Optional[str] = None
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_REGION: str = "us-east-1"
    CLOUDFLARE_R2_ENDPOINT: Optional[str] = None
    CLOUDFLARE_R2_ACCESS_KEY: Optional[str] = None
    CLOUDFLARE_R2_SECRET_KEY: Optional[str] = None
    CLOUDFLARE_R2_PUBLIC_DOMAIN: Optional[str] = None

    # CDN Configuration
    LOCAL_CDN_PATH: str = "cdn_storage"
    LOCAL_CDN_URL: str = "http://localhost:8000/cdn"

    # Local Storage Configuration
    LOCAL_STORAGE_PATH: str = "./storage"
    LOCAL_STORAGE_BASE_URL: str = "http://localhost:8000/storage"

    # OAuth Configuration
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[str] = None
    GITHUB_CLIENT_ID: Optional[str] = None
    GITHUB_CLIENT_SECRET: Optional[str] = None

    # Stripe Configuration
    STRIPE_SECRET_KEY: Optional[str] = None
    STRIPE_WEBHOOK_SECRET: Optional[str] = None
    STRIPE_STARTER_PRICE_ID: Optional[str] = None
    STRIPE_PROFESSIONAL_PRICE_ID: Optional[str] = None
    STRIPE_ENTERPRISE_PRICE_ID: Optional[str] = None
    STRIPE_VIDEO_GENERATION_PRICE_ID: Optional[str] = None
    STRIPE_STORAGE_PRICE_ID: Optional[str] = None
    STRIPE_BANDWIDTH_PRICE_ID: Optional[str] = None

    # JWT Configuration
    REFRESH_TOKEN_EXPIRE_DAYS: int = 30

    # Frontend URL
    FRONTEND_URL: str = "http://localhost:3000"

    # Logging & Monitoring
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"  # json or text
    LOG_DIR: str = "./logs"  # Directory for log files
    LOG_MAX_BYTES: int = 10 * 1024 * 1024  # 10MB per log file
    LOG_BACKUP_COUNT: int = 5  # Keep 5 backup log files

    # Admin Configuration
    ADMIN_EMAILS: List[str] = ["<EMAIL>", "<EMAIL>"]

    # Email Configuration
    SMTP_SERVER: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAIL_FROM: str = "<EMAIL>"
    EMAIL_FROM_NAME: str = "Your App"


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()