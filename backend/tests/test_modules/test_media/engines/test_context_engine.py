"""
Tests for context engine.
Tests product context analysis and enhancement functionality.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from modules.media.engines.context_engine import EcommerceContextEngine
from modules.media.schemas import ProductContext, ProductCategory, TargetAudience, ContentStyle


class TestContextEngine:
    """Test ContextEngine functionality."""
    
    @pytest.fixture
    def context_engine(self):
        """Create a context engine instance for testing."""
        return EcommerceContextEngine()
    
    @pytest.fixture
    def sample_product_context(self):
        """Create sample product context for testing."""
        return ProductContext(
            title="Premium Wireless Headphones",
            description="High-quality wireless headphones with noise cancellation",
            category=ProductCategory.ELECTRONICS,
            price=199.99,
            currency="USD",
            brand="AudioTech",
            target_audience=[TargetAudience.PROFESSIONALS],
            key_features=["noise cancellation", "wireless", "long battery"],
            style_preferences=ContentStyle.PROFESSIONAL
        )
    
    @pytest.mark.asyncio
    async def test_analyze_product_context(self, context_engine, sample_product_context):
        """Test product context analysis."""
        result = await context_engine.analyze_product_context(sample_product_context)
        
        assert isinstance(result, dict)
        assert "enhanced_description" in result
        assert "visual_elements" in result
        assert "target_keywords" in result
        assert "style_recommendations" in result
        
        # Check that analysis includes relevant information
        assert "wireless" in result["enhanced_description"].lower()
        assert "headphones" in result["enhanced_description"].lower()
        assert len(result["target_keywords"]) > 0
        assert len(result["visual_elements"]) > 0
    
    @pytest.mark.asyncio
    async def test_enhance_product_description(self, context_engine, sample_product_context):
        """Test product description enhancement."""
        enhanced = await context_engine.enhance_product_description(sample_product_context)
        
        assert isinstance(enhanced, str)
        assert len(enhanced) > len(sample_product_context.description)
        assert "wireless" in enhanced.lower()
        assert "headphones" in enhanced.lower()
        
        # Should include brand and key features
        assert sample_product_context.brand.lower() in enhanced.lower()
        assert any(feature in enhanced.lower() for feature in sample_product_context.key_features)
    
    @pytest.mark.asyncio
    async def test_extract_visual_elements(self, context_engine, sample_product_context):
        """Test visual elements extraction."""
        elements = await context_engine.extract_visual_elements(sample_product_context)
        
        assert isinstance(elements, list)
        assert len(elements) > 0
        
        # Should include relevant visual elements for headphones
        elements_str = " ".join(elements).lower()
        assert any(term in elements_str for term in ["sleek", "modern", "professional", "black", "white"])
    
    @pytest.mark.asyncio
    async def test_generate_target_keywords(self, context_engine, sample_product_context):
        """Test target keywords generation."""
        keywords = await context_engine.generate_target_keywords(sample_product_context)
        
        assert isinstance(keywords, list)
        assert len(keywords) > 0
        
        # Should include product-relevant keywords
        keywords_str = " ".join(keywords).lower()
        assert "wireless" in keywords_str
        assert "headphones" in keywords_str
        assert "audio" in keywords_str or "sound" in keywords_str
    
    @pytest.mark.asyncio
    async def test_get_style_recommendations(self, context_engine, sample_product_context):
        """Test style recommendations."""
        recommendations = await context_engine.get_style_recommendations(sample_product_context)
        
        assert isinstance(recommendations, dict)
        assert "color_scheme" in recommendations
        assert "composition" in recommendations
        assert "lighting" in recommendations
        assert "mood" in recommendations
        
        # Professional style should have appropriate recommendations
        assert "professional" in recommendations["mood"].lower()
    
    @pytest.mark.asyncio
    async def test_context_analysis_different_categories(self, context_engine):
        """Test context analysis for different product categories."""
        # Fashion product
        fashion_context = ProductContext(
            title="Designer Dress",
            description="Elegant evening dress",
            category=ProductCategory.FASHION_APPAREL,
            price=299.99,
            currency="USD",
            style_preferences=ContentStyle.LUXURY
        )
        
        fashion_result = await context_engine.analyze_product_context(fashion_context)
        
        # Electronics product
        electronics_context = ProductContext(
            title="Smartphone",
            description="Latest smartphone with advanced features",
            category=ProductCategory.ELECTRONICS,
            price=899.99,
            currency="USD",
            style_preferences=ContentStyle.MODERN
        )
        
        electronics_result = await context_engine.analyze_product_context(electronics_context)
        
        # Results should be different based on category
        assert fashion_result["style_recommendations"] != electronics_result["style_recommendations"]
        assert fashion_result["visual_elements"] != electronics_result["visual_elements"]
    
    @pytest.mark.asyncio
    async def test_context_analysis_different_audiences(self, context_engine):
        """Test context analysis for different target audiences."""
        # Gen Z audience
        gen_z_context = ProductContext(
            title="Trendy Sneakers",
            description="Stylish sneakers for young adults",
            category=ProductCategory.FOOTWEAR,
            price=129.99,
            currency="USD",
            target_audience=[TargetAudience.GEN_Z],
            style_preferences=ContentStyle.PLAYFUL
        )
        
        gen_z_result = await context_engine.analyze_product_context(gen_z_context)
        
        # Luxury buyers audience
        luxury_context = ProductContext(
            title="Premium Watch",
            description="Luxury timepiece with Swiss movement",
            category=ProductCategory.LUXURY_GOODS,
            price=2999.99,
            currency="USD",
            target_audience=[TargetAudience.LUXURY_BUYERS],
            style_preferences=ContentStyle.LUXURY
        )
        
        luxury_result = await context_engine.analyze_product_context(luxury_context)
        
        # Results should reflect different audience preferences
        assert "trendy" in gen_z_result["enhanced_description"].lower() or "young" in gen_z_result["enhanced_description"].lower()
        assert "luxury" in luxury_result["enhanced_description"].lower() or "premium" in luxury_result["enhanced_description"].lower()
    
    @pytest.mark.asyncio
    async def test_context_engine_error_handling(self, context_engine):
        """Test context engine error handling."""
        # Test with minimal context
        minimal_context = ProductContext(
            title="Test Product",
            description="Test description",
            category=ProductCategory.ELECTRONICS,
            price=99.99,
            currency="USD"
        )
        
        # Should still work with minimal information
        result = await context_engine.analyze_product_context(minimal_context)
        
        assert isinstance(result, dict)
        assert "enhanced_description" in result
        assert len(result["enhanced_description"]) > 0
    
    @pytest.mark.asyncio
    async def test_context_caching(self, context_engine, sample_product_context):
        """Test context analysis caching."""
        # First call
        result1 = await context_engine.analyze_product_context(sample_product_context)
        
        # Second call with same context should use cache
        result2 = await context_engine.analyze_product_context(sample_product_context)
        
        # Results should be identical (from cache)
        assert result1 == result2
    
    @pytest.mark.asyncio
    async def test_multilingual_context_analysis(self, context_engine):
        """Test context analysis for different languages."""
        # Spanish product context
        spanish_context = ProductContext(
            title="Auriculares Inalámbricos Premium",
            description="Auriculares inalámbricos de alta calidad con cancelación de ruido",
            category=ProductCategory.ELECTRONICS,
            price=199.99,
            currency="EUR",
            style_preferences=ContentStyle.PROFESSIONAL
        )
        
        result = await context_engine.analyze_product_context(spanish_context)
        
        assert isinstance(result, dict)
        assert "enhanced_description" in result
        assert len(result["enhanced_description"]) > 0
        
        # Should handle Spanish text appropriately
        assert "auriculares" in result["enhanced_description"].lower() or "headphones" in result["enhanced_description"].lower()
    
    def test_context_engine_initialization(self, context_engine):
        """Test context engine initialization."""
        assert context_engine is not None
        assert hasattr(context_engine, 'analyze_product_context')
        assert hasattr(context_engine, 'enhance_product_description')
        assert hasattr(context_engine, 'extract_visual_elements')
        assert hasattr(context_engine, 'generate_target_keywords')
        assert hasattr(context_engine, 'get_style_recommendations')
