"""
Tests for prompt engine.
Tests AI prompt generation for different media types.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from modules.media.engines.prompt_engine import PromptEngine
from modules.media.schemas import (
    ProductContext, ProductCategory, TargetAudience, ContentStyle,
    ProviderMediaRequest, GenerationSettings
)


class TestPromptEngine:
    """Test PromptEngine functionality."""
    
    @pytest.fixture
    def prompt_engine(self):
        """Create a prompt engine instance for testing."""
        return PromptEngine()
    
    @pytest.fixture
    def sample_provider_request(self):
        """Create sample provider request for testing."""
        product_context = ProductContext(
            title="Premium Wireless Headphones",
            description="High-quality wireless headphones with noise cancellation",
            category=ProductCategory.ELECTRONICS,
            price=199.99,
            currency="USD",
            brand="AudioTech",
            target_audience=[TargetAudience.PROFESSIONALS],
            key_features=["noise cancellation", "wireless", "long battery"],
            style_preferences=ContentStyle.PROFESSIONAL
        )
        
        return ProviderMediaRequest(
            product_title="Premium Wireless Headphones",
            product_description="High-quality wireless headphones with noise cancellation",
            product_context=product_context,
            media_type="image",
            aspect_ratio="1:1",
            style="professional",
            language="en"
        )
    
    @pytest.mark.asyncio
    async def test_generate_image_prompt(self, prompt_engine, sample_provider_request):
        """Test image prompt generation."""
        prompt = await prompt_engine.generate_image_prompt(sample_provider_request)
        
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        
        # Should include product information
        assert "headphones" in prompt.lower()
        assert "wireless" in prompt.lower()
        assert "professional" in prompt.lower()
        
        # Should include technical specifications
        assert "1:1" in prompt or "square" in prompt.lower()
    
    @pytest.mark.asyncio
    async def test_generate_video_prompt(self, prompt_engine, sample_provider_request):
        """Test video prompt generation."""
        video_request = sample_provider_request.model_copy()
        video_request.media_type = "video"
        video_request.aspect_ratio = "16:9"
        
        prompt = await prompt_engine.generate_video_prompt(video_request)
        
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        
        # Should include video-specific elements
        assert "video" in prompt.lower() or "motion" in prompt.lower() or "scene" in prompt.lower()
        assert "headphones" in prompt.lower()
        assert "16:9" in prompt or "widescreen" in prompt.lower()
    
    @pytest.mark.asyncio
    async def test_generate_text_prompt(self, prompt_engine, sample_provider_request):
        """Test text prompt generation."""
        text_request = sample_provider_request.model_copy()
        text_request.media_type = "text"
        
        prompt = await prompt_engine.generate_text_prompt(text_request)
        
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        
        # Should include text generation instructions
        assert "write" in prompt.lower() or "create" in prompt.lower() or "generate" in prompt.lower()
        assert "headphones" in prompt.lower()
        assert "professional" in prompt.lower()
    
    @pytest.mark.asyncio
    async def test_enhance_prompt_with_context(self, prompt_engine, sample_provider_request):
        """Test prompt enhancement with context."""
        base_prompt = "Create an image of wireless headphones"
        
        enhanced_prompt = await prompt_engine.enhance_prompt_with_context(
            base_prompt, 
            sample_provider_request
        )
        
        assert isinstance(enhanced_prompt, str)
        assert len(enhanced_prompt) > len(base_prompt)
        
        # Should include context information
        assert "audiotech" in enhanced_prompt.lower() or "brand" in enhanced_prompt.lower()
        assert "professional" in enhanced_prompt.lower()
        assert "noise cancellation" in enhanced_prompt.lower()
    
    @pytest.mark.asyncio
    async def test_apply_style_guidelines(self, prompt_engine):
        """Test style guidelines application."""
        base_prompt = "Create a product image"
        
        # Test different styles
        professional_prompt = await prompt_engine.apply_style_guidelines(
            base_prompt, 
            ContentStyle.PROFESSIONAL
        )
        
        luxury_prompt = await prompt_engine.apply_style_guidelines(
            base_prompt, 
            ContentStyle.LUXURY
        )
        
        playful_prompt = await prompt_engine.apply_style_guidelines(
            base_prompt, 
            ContentStyle.PLAYFUL
        )
        
        # Each style should produce different prompts
        assert professional_prompt != luxury_prompt
        assert luxury_prompt != playful_prompt
        assert professional_prompt != playful_prompt
        
        # Should contain style-specific keywords
        assert "professional" in professional_prompt.lower() or "clean" in professional_prompt.lower()
        assert "luxury" in luxury_prompt.lower() or "premium" in luxury_prompt.lower()
        assert "playful" in playful_prompt.lower() or "fun" in playful_prompt.lower()
    
    @pytest.mark.asyncio
    async def test_add_technical_specifications(self, prompt_engine):
        """Test technical specifications addition."""
        base_prompt = "Create a product image"
        
        specs = {
            "aspect_ratio": "16:9",
            "quality": "high",
            "lighting": "studio",
            "background": "white"
        }
        
        enhanced_prompt = await prompt_engine.add_technical_specifications(base_prompt, specs)
        
        assert isinstance(enhanced_prompt, str)
        assert len(enhanced_prompt) > len(base_prompt)
        
        # Should include technical specifications
        assert "16:9" in enhanced_prompt or "widescreen" in enhanced_prompt.lower()
        assert "high quality" in enhanced_prompt.lower() or "high resolution" in enhanced_prompt.lower()
        assert "studio lighting" in enhanced_prompt.lower() or "professional lighting" in enhanced_prompt.lower()
        assert "white background" in enhanced_prompt.lower()
    
    @pytest.mark.asyncio
    async def test_optimize_for_provider(self, prompt_engine, sample_provider_request):
        """Test prompt optimization for different providers."""
        base_prompt = "Create an image of wireless headphones"
        
        # Test optimization for different providers
        banana_prompt = await prompt_engine.optimize_for_provider(base_prompt, "banana")
        gemini_prompt = await prompt_engine.optimize_for_provider(base_prompt, "gemini")
        veo3_prompt = await prompt_engine.optimize_for_provider(base_prompt, "veo3")
        
        # Each provider should have optimized prompts
        assert isinstance(banana_prompt, str)
        assert isinstance(gemini_prompt, str)
        assert isinstance(veo3_prompt, str)
        
        # Prompts should be different for different providers
        assert banana_prompt != gemini_prompt or banana_prompt != veo3_prompt
    
    @pytest.mark.asyncio
    async def test_generate_prompt_for_category(self, prompt_engine):
        """Test prompt generation for different product categories."""
        # Fashion category
        fashion_request = ProviderMediaRequest(
            product_title="Designer Dress",
            product_description="Elegant evening dress",
            product_context=ProductContext(
                title="Designer Dress",
                description="Elegant evening dress",
                category=ProductCategory.FASHION_APPAREL,
                price=299.99,
                currency="USD",
                target_audience=[]
            ),
            media_type="image",
            style="luxury"
        )
        
        fashion_prompt = await prompt_engine.generate_image_prompt(fashion_request)
        
        # Electronics category
        electronics_request = ProviderMediaRequest(
            product_title="Smartphone",
            product_description="Latest smartphone",
            product_context=ProductContext(
                title="Smartphone",
                description="Latest smartphone",
                category=ProductCategory.ELECTRONICS,
                price=899.99,
                currency="USD",
                target_audience=[]
            ),
            media_type="image",
            style="modern"
        )
        
        electronics_prompt = await prompt_engine.generate_image_prompt(electronics_request)
        
        # Prompts should be different for different categories
        assert fashion_prompt != electronics_prompt
        assert "dress" in fashion_prompt.lower() or "fashion" in fashion_prompt.lower()
        assert "smartphone" in electronics_prompt.lower() or "phone" in electronics_prompt.lower()
    
    @pytest.mark.asyncio
    async def test_multilingual_prompt_generation(self, prompt_engine):
        """Test prompt generation for different languages."""
        # Spanish request
        spanish_request = ProviderMediaRequest(
            product_title="Auriculares Inalámbricos",
            product_description="Auriculares de alta calidad",
            product_context=ProductContext(
                title="Auriculares Inalámbricos",
                description="Auriculares de alta calidad",
                category=ProductCategory.ELECTRONICS,
                price=199.99,
                currency="EUR",
                target_audience=[]
            ),
            media_type="image",
            language="es"
        )
        
        spanish_prompt = await prompt_engine.generate_image_prompt(spanish_request)
        
        assert isinstance(spanish_prompt, str)
        assert len(spanish_prompt) > 0
        
        # Should handle Spanish content appropriately
        assert "auriculares" in spanish_prompt.lower() or "headphones" in spanish_prompt.lower()
    
    @pytest.mark.asyncio
    async def test_custom_prompt_integration(self, prompt_engine, sample_provider_request):
        """Test integration of custom prompts."""
        custom_prompt = "Show the headphones in a modern office setting"
        request_with_custom = sample_provider_request.model_copy()
        request_with_custom.custom_prompt = custom_prompt
        
        prompt = await prompt_engine.generate_image_prompt(request_with_custom)
        
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        
        # Should include custom prompt elements
        assert "office" in prompt.lower() or "modern" in prompt.lower()
        assert "headphones" in prompt.lower()
    
    @pytest.mark.asyncio
    async def test_prompt_length_optimization(self, prompt_engine, sample_provider_request):
        """Test prompt length optimization."""
        # Generate prompt and check length is reasonable
        prompt = await prompt_engine.generate_image_prompt(sample_provider_request)
        
        # Prompt should be detailed but not excessively long
        assert 50 <= len(prompt) <= 2000  # Reasonable length range
        
        # Should be well-structured
        assert not prompt.startswith(" ")  # No leading whitespace
        assert not prompt.endswith(" ")   # No trailing whitespace
    
    def test_prompt_engine_initialization(self, prompt_engine):
        """Test prompt engine initialization."""
        assert prompt_engine is not None
        assert hasattr(prompt_engine, 'generate_image_prompt')
        assert hasattr(prompt_engine, 'generate_video_prompt')
        assert hasattr(prompt_engine, 'generate_text_prompt')
        assert hasattr(prompt_engine, 'enhance_prompt_with_context')
        assert hasattr(prompt_engine, 'apply_style_guidelines')
        assert hasattr(prompt_engine, 'add_technical_specifications')
        assert hasattr(prompt_engine, 'optimize_for_provider')
