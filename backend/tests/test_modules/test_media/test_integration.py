"""
Integration tests for media module.
Tests end-to-end workflows and component interactions.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
import json
from datetime import datetime

from modules.media.service import media_service
from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.media.schemas import (
    MediaGenerateRequest, ProductItem, ProductContext, ProductCategory,
    GenerationSettings, ProviderMediaResult
)
from modules.auth.models import User


class TestMediaGenerationWorkflow:
    """Test complete media generation workflow."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_image_generation(self, db_session: AsyncSession, test_user: User, banana_test_data):
        """Test complete image generation workflow."""
        # Create request
        product_context = ProductContext(
            title="Premium Wireless Headphones",
            description="High-quality wireless headphones with noise cancellation",
            category=ProductCategory.ELECTRONICS,
            price=199.99,
            currency="USD"
        )
        
        item = ProductItem(
            product_id=123,
            product_context=product_context
        )
        
        request = MediaGenerateRequest(
            mode="image",
            media_type="image",
            model="banana",
            items=[item],
            product_ids=[123]
        )
        
        # Mock provider response
        mock_result = ProviderMediaResult(
            success=True,
            provider_job_id="e2e_banana_123",
            images=banana_test_data.get("images", []),
            estimated_completion_time=30,
            quality_score=88.5
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            with patch('modules.storage.storage_service.media_storage_service.upload_media') as mock_upload:
                
                # Setup mocks
                mock_generate.return_value = mock_result
                from modules.storage.storage_service import MediaFile, MediaFormat
                mock_upload.return_value = MediaFile(
                    file_id="test_file_123",
                    tenant_id=1,
                    original_filename="test_image.jpg",
                    storage_path="tenant_1/2024/01/test_file_123.jpg",
                    public_url="https://storage.example.com/uploaded_image.jpg",
                    file_size=1024,
                    format=MediaFormat.MP4,
                    created_at=datetime.utcnow()
                )
                
                # Create jobs
                jobs = await media_service.create_generation_jobs(
                    db=db_session,
                    user_id=test_user.id,
                    request=request
                )
                
                assert len(jobs) == 1
                job = jobs[0]
                assert job.status == MediaJobStatus.PENDING
                assert job.media_type == "image"
                assert job.provider == "banana"
                
                # Verify job was created successfully
                assert job is not None
                assert job.id is not None
    
    @pytest.mark.asyncio
    async def test_end_to_end_video_generation(self, db_session: AsyncSession, test_user: User, veo3_test_data):
        """Test complete video generation workflow."""
        product_context = ProductContext(
            title="Athletic Running Shoes",
            description="Professional running shoes for athletes",
            category=ProductCategory.FOOTWEAR,
            price=149.99,
            currency="USD"
        )
        
        item = ProductItem(
            product_id=124,
            product_context=product_context
        )
        
        request = MediaGenerateRequest(
            mode="video",
            media_type="video",
            model="veo3",
            items=[item],
            product_ids=[124]
        )
        
        # Mock provider response
        mock_result = ProviderMediaResult(
            success=True,
            provider_job_id="e2e_veo3_124",
            videos=[{"video_url": veo3_test_data["variants"][0]["video_url"]}] if veo3_test_data.get("variants") else [],
            estimated_completion_time=120
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            mock_generate.return_value = mock_result
            
            # Create and process job
            jobs = await media_service.create_generation_jobs(
                db=db_session,
                user_id=test_user.id,
                request=request
            )
            
            job = jobs[0]
            assert job.media_type == "video"
            assert job.provider == "veo3"
            
            # Verify job was created successfully
            assert job is not None
            assert job.id is not None
    
    @pytest.mark.asyncio
    async def test_end_to_end_text_generation(self, db_session: AsyncSession, test_user: User, gemini_test_data):
        """Test complete text generation workflow."""
        product_context = ProductContext(
            title="Luxury Watch",
            description="Premium timepiece with Swiss movement",
            category=ProductCategory.LUXURY_GOODS,
            price=2999.99,
            currency="USD"
        )
        
        item = ProductItem(
            product_id=125,
            product_context=product_context
        )
        
        request = MediaGenerateRequest(
            mode="text",
            media_type="text",
            model="gemini",
            items=[item],
            product_ids=[125]
        )
        
        # Mock provider response
        mock_result = ProviderMediaResult(
            success=True,
            provider_job_id="e2e_gemini_125",
            texts=[{"content": gemini_test_data["variants"][0]["text"]}] if gemini_test_data.get("variants") else []
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            mock_generate.return_value = mock_result
            
            # Create and process job
            jobs = await media_service.create_generation_jobs(
                db=db_session,
                user_id=test_user.id,
                request=request
            )
            
            job = jobs[0]
            assert job.media_type == "text"
            assert job.provider == "gemini"
            
            # Verify job was created successfully
            assert job is not None
            assert job.id is not None


class TestMultiProductWorkflow:
    """Test workflows with multiple products."""
    
    @pytest.mark.asyncio
    async def test_batch_generation_workflow(self, db_session: AsyncSession, test_user: User):
        """Test batch generation for multiple products."""
        # Create multiple product contexts
        products = [
            ProductContext(
                title="Product 1",
                description="First product",
                category=ProductCategory.ELECTRONICS,
                price=99.99,
                currency="USD"
            ),
            ProductContext(
                title="Product 2", 
                description="Second product",
                category=ProductCategory.FASHION_APPAREL,
                price=79.99,
                currency="USD"
            ),
            ProductContext(
                title="Product 3",
                description="Third product",
                category=ProductCategory.HOME_DECOR,
                price=149.99,
                currency="USD"
            )
        ]
        
        items = [
            ProductItem(product_id=i+1, product_context=product)
            for i, product in enumerate(products)
        ]
        
        request = MediaGenerateRequest(
            mode="image",
            media_type="image",
            model="banana",
            items=items,
            product_ids=[1, 2, 3]
        )
        
        # Mock successful generation for all products
        mock_result = ProviderMediaResult(
            success=True,
            provider_job_id="batch_job_123",
            images=[{"image_url": f"https://example.com/product_{i}.jpg"} for i in range(3)]
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            mock_generate.return_value = mock_result
            
            # Create jobs for all products
            jobs = await media_service.create_generation_jobs(
                db=db_session,
                user_id=test_user.id,
                request=request
            )
            
            # Should create one job per product
            assert len(jobs) == 3
            
            # All jobs should be for the same user
            for job in jobs:
                assert job.user_id == test_user.id
                assert job.status == MediaJobStatus.PENDING
                assert job.media_type == "image"
            
            # Product IDs should match
            product_ids = [job.product_id for job in jobs]
            assert set(product_ids) == {1, 2, 3}


class TestErrorRecoveryWorkflow:
    """Test error recovery and fallback workflows."""
    
    @pytest.mark.asyncio
    async def test_provider_fallback_workflow(self, db_session: AsyncSession, test_user: User):
        """Test fallback to secondary provider when primary fails."""
        product_context = ProductContext(
            title="Fallback Test Product",
            description="Testing provider fallback",
            category=ProductCategory.ELECTRONICS,
            price=199.99,
            currency="USD"
        )
        
        item = ProductItem(
            product_id=126,
            product_context=product_context
        )
        
        request = MediaGenerateRequest(
            mode="image",
            media_type="image",
            model="banana",  # Primary provider
            items=[item],
            product_ids=[126]
        )
        
        # Primary provider fails
        failed_result = ProviderMediaResult(
            success=False,
            error_message="Primary provider unavailable",
            error_code="SERVICE_UNAVAILABLE"
        )
        
        # Fallback provider succeeds
        success_result = ProviderMediaResult(
            success=True,
            provider_job_id="fallback_job_126",
            images=[{"image_url": "https://example.com/fallback_image.jpg"}]
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            # First call fails, second succeeds
            mock_generate.side_effect = [failed_result, success_result]
            
            # Create job
            jobs = await media_service.create_generation_jobs(
                db=db_session,
                user_id=test_user.id,
                request=request
            )
            
            job = jobs[0]
            
            # Simulate processing with fallback
            result = await media_service.generate_media_with_provider(
                provider_name="banana",
                request=request,
                use_fallback=True
            )

            # Should eventually succeed with fallback
            if not result.success:
                # Try fallback
                result = await media_service.generate_media_with_provider(
                    provider_name="mock",  # Fallback provider
                    request=request,
                    use_fallback=False
                )

            assert result.success is True
            assert result.provider_job_id == "fallback_job_126"
    
    @pytest.mark.asyncio
    async def test_job_retry_workflow(self, db_session: AsyncSession, test_user: User):
        """Test job retry workflow."""
        # Create a failed job
        failed_job = MediaJob(
            user_id=test_user.id,
            product_id=127,
            status=MediaJobStatus.FAILED,
            media_type="image",
            provider="banana",
            error_message="Temporary provider error"
        )
        db_session.add(failed_job)
        await db_session.commit()
        await db_session.refresh(failed_job)
        
        # Mock successful retry
        retry_result = ProviderMediaResult(
            success=True,
            provider_job_id="retry_job_127",
            images=[{"image_url": "https://example.com/retry_image.jpg"}]
        )
        
        # Mock successful retry
        retry_result = ProviderMediaResult(
            success=True,
            provider_job_id="retry_job_127",
            images=[{"image_url": "https://example.com/retry_image.jpg"}]
        )

        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            # Setup mocks
            mock_generate.return_value = retry_result

            # Create new job for retry
            retry_job = MediaJob(
                user_id=test_user.id,
                product_id=127,
                status=MediaJobStatus.PENDING,
                media_type="image",
                provider="banana"
            )
            db_session.add(retry_job)
            await db_session.commit()
            await db_session.refresh(retry_job)

            # Verify the retry job was created
            assert retry_job is not None
            assert retry_job.status == MediaJobStatus.PENDING
            assert retry_job.product_id == 127


class TestPerformanceWorkflow:
    """Test performance-related workflows."""
    
    @pytest.mark.asyncio
    async def test_concurrent_job_processing(self, db_session: AsyncSession, test_user: User):
        """Test concurrent processing of multiple jobs."""
        import asyncio
        
        # Create multiple jobs
        job_count = 5
        jobs = []
        
        for i in range(job_count):
            product_context = ProductContext(
                title=f"Concurrent Product {i}",
                description=f"Product for concurrent test {i}",
                category=ProductCategory.ELECTRONICS,
                price=99.99 + i,
                currency="USD"
            )
            
            item = ProductItem(
                product_id=200 + i,
                product_context=product_context
            )
            
            request = MediaGenerateRequest(
                mode="image",
                media_type="image",
                model="banana",
                items=[item],
                product_ids=[200 + i]
            )
            
            job_list = await media_service.create_generation_jobs(
                db=db_session,
                user_id=test_user.id,
                request=request
            )
            jobs.extend(job_list)
        
        assert len(jobs) == job_count
        
        # All jobs should be created successfully
        for job in jobs:
            assert job.status == MediaJobStatus.PENDING
            assert job.user_id == test_user.id
