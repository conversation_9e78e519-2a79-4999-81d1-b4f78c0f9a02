"""
Tests for media processor.
Tests media job processing functionality.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from modules.media.processors.media_processor import MediaProcessor
from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.media.schemas import ProviderMediaResult
from modules.auth.models import User, Tenant


class TestMediaProcessor:
    """Test MediaProcessor functionality."""
    
    @pytest.fixture
    def media_processor(self):
        """Create a media processor instance for testing."""
        return MediaProcessor()
    
    @pytest.fixture
    async def sample_job_data(self, db_session: AsyncSession, test_user: User, test_tenant: Tenant):
        """Create sample job data for testing."""
        # Create a MediaJob in the database
        media_job = MediaJob()
        media_job.tenant_id = test_tenant.id
        media_job.user_id = test_user.id
        media_job.media_type = "image"
        media_job.status = MediaJobStatus.PENDING
        media_job.product_ids = ["product_123", "product_456"]
        media_job.product_id = 12345  # Use integer instead of string
        media_job.provider = "banana"  # Set default provider
        db_session.add(media_job)
        await db_session.commit()
        await db_session.refresh(media_job)

        return {
            "job_id": media_job.id,
            "tenant_id": test_tenant.id,
            "product_ids": ["product_123", "product_456"],
            "media_type": "image",
            "template_id": "default_template",
            "voice_id": "default_voice",
            "custom_config": {
                "aspect_ratio": "1:1",
                "style": "professional"
            }
        }
    
    @pytest.mark.asyncio
    async def test_process_job_success(self, media_processor, sample_job_data, db_session: AsyncSession):
        """Test successful job processing."""
        mock_result = ProviderMediaResult(
            success=True,
            provider_job_id="banana_job_123",
            images=[
                {"image_url": "https://example.com/image1.jpg"},
                {"image_url": "https://example.com/image2.jpg"}
            ],
            estimated_completion_time=30,
            quality_score=88.5
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            with patch.object(media_processor, 'get_db_session') as mock_db:
                
                # Setup mocks
                mock_db.return_value.__aenter__.return_value = db_session
                mock_db.return_value.__aexit__.return_value = None
                mock_generate.return_value = mock_result
                
                result = await media_processor._process_async(sample_job_data)
                
                assert result.get("success") is True
                assert result.get("provider_job_id") == "banana_job_123"
                assert len(result.get("images", [])) == 2
    
    @pytest.mark.asyncio
    async def test_process_job_provider_failure(self, media_processor, sample_job_data, db_session: AsyncSession):
        """Test job processing with provider failure."""
        mock_result = ProviderMediaResult(
            success=False,
            error_message="Provider API rate limit exceeded",
            error_code="RATE_LIMIT_EXCEEDED"
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            with patch.object(media_processor, 'get_db_session') as mock_db:
                
                # Setup mocks
                mock_db.return_value.__aenter__.return_value = db_session
                mock_db.return_value.__aexit__.return_value = None
                mock_generate.return_value = mock_result
                
                result = await media_processor._process_async(sample_job_data)
                
                assert result.get("success") is False
                assert "rate limit" in result.get("error", "").lower()
    
    @pytest.mark.asyncio
    async def test_process_job_with_fallback(self, media_processor, sample_job_data, db_session: AsyncSession):
        """Test job processing with fallback provider."""
        # First provider fails
        failed_result = ProviderMediaResult(
            success=False,
            error_message="Primary provider unavailable"
        )
        
        # Fallback provider succeeds
        success_result = ProviderMediaResult(
            success=True,
            provider_job_id="fallback_job_456",
            images=[{"image_url": "https://example.com/fallback_image.jpg"}]
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            with patch.object(media_processor, 'get_db_session') as mock_db:
                
                # Setup mocks
                mock_db.return_value.__aenter__.return_value = db_session
                mock_db.return_value.__aexit__.return_value = None
                
                # First call fails, second succeeds
                mock_generate.side_effect = [failed_result, success_result]
                
                result = await media_processor._process_async(sample_job_data)
                
                assert result.get("success") is True
                assert result.get("provider_job_id") == "fallback_job_456"
    
    @pytest.mark.asyncio
    async def test_process_job_update_progress(self, media_processor, sample_job_data, db_session: AsyncSession, test_user: User):
        """Test job progress updates during processing."""
        # Create a job in database
        
        
        mock_result = ProviderMediaResult(
            success=True,
            provider_job_id="progress_job_123",
            images=[{"image_url": "https://example.com/progress_image.jpg"}]
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            with patch.object(media_processor, 'get_db_session') as mock_db:
                
                # Setup mocks
                mock_db.return_value.__aenter__.return_value = db_session
                mock_db.return_value.__aexit__.return_value = None
                mock_generate.return_value = mock_result
                
                result = await media_processor._process_async(sample_job_data)
                
                # Verify job status in database
                updated_job = await db_session.get(MediaJob, sample_job_data["job_id"])
                assert updated_job.status == MediaJobStatus.COMPLETED
                assert result.get("success") is True
    
    @pytest.mark.asyncio
    async def test_process_job_create_variants(self, media_processor, sample_job_data, db_session: AsyncSession, test_user: User):
        """Test variant creation during job processing."""
        mock_result = ProviderMediaResult(
            success=True,
            provider_job_id="variants_job_123",
            images=[
                {"image_url": "https://example.com/square.jpg", "variant": "square"},
                {"image_url": "https://example.com/vertical.jpg", "variant": "vertical"}
            ]
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            with patch.object(media_processor, 'get_db_session') as mock_db:
                
                # Setup mocks
                mock_db.return_value.__aenter__.return_value = db_session
                mock_db.return_value.__aexit__.return_value = None
                mock_generate.return_value = mock_result
                
                result = await media_processor._process_async(sample_job_data)
                
                # Verify job and variants in database
                updated_job = await db_session.get(MediaJob, sample_job_data["job_id"])
                assert updated_job.status == MediaJobStatus.COMPLETED
                
                variants = (await db_session.execute(select(MediaVariant).filter_by(job_id=sample_job_data["job_id"]))).scalars().all()
                assert len(variants) == 2
                assert all(v.status == MediaVariantStatus.READY for v in variants)
                assert result.get("success") is True
    
    @pytest.mark.asyncio
    async def test_process_job_error_handling(self, media_processor, sample_job_data):
        """Test error handling during job processing."""
        with patch.object(media_processor, 'get_db_session') as mock_db:
            mock_db.side_effect = Exception("Database connection failed")
            
            with pytest.raises(Exception, match="Database connection failed"):
                await media_processor._process_async(sample_job_data)
    
    @pytest.mark.asyncio
    async def test_process_job_timeout_handling(self, media_processor, sample_job_data, db_session: AsyncSession):
        """Test timeout handling during job processing."""
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            with patch.object(media_processor, 'get_db_session') as mock_db:
                
                # Setup mocks
                mock_db.return_value.__aenter__.return_value = db_session
                mock_db.return_value.__aexit__.return_value = None
                mock_generate.side_effect = asyncio.TimeoutError("Operation timed out")
                
                result = await media_processor._process_async(sample_job_data)
                
                assert result.get("success") is False
                assert "timeout" in result.get("error", "").lower()
    
    @pytest.mark.asyncio
    async def test_process_job_validation(self, media_processor):
        """Test job data validation."""
        # Invalid job data (missing required fields)
        invalid_job_data = {
            "job_id": 1,
            # Missing user_id, product_id, etc.
        }
        
        with pytest.raises(KeyError):
            await media_processor._process_async(invalid_job_data)
    
    @pytest.mark.asyncio
    async def test_process_job_different_media_types(self, media_processor, test_user, db_session: AsyncSession):
        """Test processing different media types."""
        media_types = ["image", "video", "text"]
        providers = ["banana", "veo3", "gemini"]
        
        for media_type, provider in zip(media_types, providers):
            job_data = {
                "job_id": 1,
                "tenant_id": test_user.id,  # Use user_id as tenant_id for simplicity
                "user_id": test_user.id,
                "product_id": 123,
                "media_type": media_type,
                "provider": provider,
                "request_data": {
                    "product_title": f"Test {media_type} Product",
                    "product_description": f"Test {media_type} description"
                }
            }
            
            mock_result = ProviderMediaResult(
                success=True,
                provider_job_id=f"{provider}_job_123"
            )
            
            with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
                with patch.object(media_processor, 'get_db_session') as mock_db:
                    
                    # Setup mocks
                    mock_db.return_value.__aenter__.return_value = db_session
                    mock_db.return_value.__aexit__.return_value = None
                    mock_generate.return_value = mock_result
                    
                    result = await media_processor._process_async(job_data)

                    assert result.get("success") is True
                    # The result structure may be different, just check that it's successful
                    assert "job_id" in result or result.get("success") is True
    
    @pytest.mark.asyncio
    async def test_process_job_performance_monitoring(self, media_processor, sample_job_data, db_session: AsyncSession):
        """Test performance monitoring during job processing."""
        mock_result = ProviderMediaResult(
            success=True,
            provider_job_id="perf_job_123",
            images=[{"image_url": "https://example.com/perf_image.jpg"}]
        )
        
        with patch('modules.media.service.media_service.generate_media_with_provider') as mock_generate:
            with patch('modules.media.common.performance_monitor.performance_monitor.start_timer') as mock_start:
                with patch('modules.media.common.performance_monitor.performance_monitor.end_timer') as mock_end:
                    with patch.object(media_processor, 'get_db_session') as mock_db:
                        
                        # Setup mocks
                        mock_db.return_value.__aenter__.return_value = db_session
                        mock_db.return_value.__aexit__.return_value = None
                        mock_generate.return_value = mock_result
                        
                        result = await media_processor._process_async(sample_job_data)
                        
                        # Verify performance monitoring
                        assert mock_start.called
                        assert mock_end.called
                        assert result.get("success") is True
    
    def test_media_processor_initialization(self, media_processor):
        """Test media processor initialization."""
        assert media_processor is not None
        assert hasattr(media_processor, 'process')
        assert hasattr(media_processor, 'get_db_session')
