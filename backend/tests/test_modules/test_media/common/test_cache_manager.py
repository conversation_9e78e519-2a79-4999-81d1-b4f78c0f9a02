"""
Tests for cache manager.
Tests caching functionality for media operations.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
import asyncio
from datetime import datetime, timedelta, timezone

from modules.media.common.cache_manager import MediaCacheManager

# Dummy data for product_context and generation_params
DUMMY_PRODUCT_CONTEXT = {
    "title": "Test Product",
    "category": "Electronics",
    "colors": ["red", "blue"],
    "materials": ["plastic"],
    "key_features": ["feature1", "feature2"],
    "price_tier": "mid"
}

DUMMY_GENERATION_PARAMS = {
    "media_type": "image",
    "aspect_ratio": "16:9",
    "style": "realistic",
    "template_id": "template_1",
    "quality": "high"
}
DUMMY_PROVIDER = "test_provider"



class TestMediaCacheManager:
    """Test MediaCacheManager functionality."""
    
    @pytest.fixture
    def cache_manager(self):
        """Create a cache manager instance for testing."""
        return MediaCacheManager()
    
    @pytest.mark.asyncio
    async def test_set_and_get_cache(self, cache_manager):
        """Test basic cache set and get operations."""
        value = {"data": "test_value", "timestamp": datetime.now(timezone.utc).isoformat()}
        
        # Set cache
        await cache_manager.set(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER, value, ttl_seconds=300)
        
        # Get cache
        cached_value = await cache_manager.get(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER)
        
        assert cached_value is not None
        assert cached_value["data"] == "test_value"
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self, cache_manager):
        """Test cache expiration functionality."""
        value = {"data": "expiring_value"}
        # Set cache with short TTL
        await cache_manager.set(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER, value, ttl_seconds=1)
        
        # Should be available immediately
        cached_value = await cache_manager.get(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER)
        assert cached_value is not None
        
        # Wait for expiration
        await asyncio.sleep(2)
        
        # Should be expired
        expired_value = await cache_manager.get(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER)
        assert expired_value is None


    @pytest.mark.asyncio
    async def test_cache_exists(self, cache_manager):
        """Test cache existence check."""
        # Should not exist initially
        exists_before = await cache_manager.get(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER)
        assert exists_before is None
        
        # Set cache
        await cache_manager.set(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER, {"data": "exists_value"})
        
        # Should exist now
        exists_after = await cache_manager.get(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER)
        assert exists_after is not None
    
    
    @pytest.mark.asyncio
    async def test_cache_with_prefix(self, cache_manager):
        """Test cache operations with key prefixes."""
        # The cache manager generates keys with prefixes internally.
        # We just need to ensure setting and getting with product context and params works.
        value = {"job_id": 123, "status": "processing"}
        
        # Set with dummy context
        await cache_manager.set(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER, value)
        
        # Get with dummy context
        cached_value = await cache_manager.get(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER)
        assert cached_value is not None
        assert cached_value["job_id"] == 123
    
    @pytest.mark.asyncio
    async def test_cache_serialization(self, cache_manager):
        """Test cache serialization of complex objects."""
        complex_value = {
            "nested": {
                "data": [1, 2, 3],
                "metadata": {
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "version": "1.0"
                }
            },
            "list_data": ["item1", "item2", "item3"],
            "boolean_flag": True,
            "numeric_value": 42.5
        }
        
        # Set complex object
        await cache_manager.set(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER, complex_value)
        
        # Get and verify structure
        cached_value = await cache_manager.get(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER)
        assert cached_value is not None
        assert cached_value["nested"]["data"] == [1, 2, 3]
        assert cached_value["list_data"] == ["item1", "item2", "item3"]
        assert cached_value["boolean_flag"] is True
        assert cached_value["numeric_value"] == 42.5
    
    @pytest.mark.asyncio
    async def test_cache_concurrent_access(self, cache_manager):
        """Test concurrent cache access."""
        async def set_cache(value):
            product_context = {**DUMMY_PRODUCT_CONTEXT, "title": f"Test Product {value}"}
            await cache_manager.set(product_context, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER, {"data": value})
        
        async def get_cache(value):
            product_context = {**DUMMY_PRODUCT_CONTEXT, "title": f"Test Product {value}"}
            return await cache_manager.get(product_context, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER)
        
        # Concurrent set operations
        await asyncio.gather(*[set_cache(i) for i in range(10)])
        
        # Concurrent get operations
        results = await asyncio.gather(*[get_cache(i) for i in range(10)])
        
        # Verify all operations succeeded
        for i, result in enumerate(results):
            assert result is not None
            assert result["data"] == i
    
    @pytest.mark.asyncio
    async def test_cache_memory_management(self, cache_manager):
        """Test cache memory management."""
        # Set many cache entries
        for i in range(100):
            product_context = {**DUMMY_PRODUCT_CONTEXT, "id": i}
            value = {"data": f"value_{i}", "large_data": "x" * 1000}  # 1KB per entry
            await cache_manager.set(product_context, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER, value)
        
        # Verify some entries exist
        assert await cache_manager.get({**DUMMY_PRODUCT_CONTEXT, "id": 0}, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER) is not None
        assert await cache_manager.get({**DUMMY_PRODUCT_CONTEXT, "id": 50}, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER) is not None
        assert await cache_manager.get({**DUMMY_PRODUCT_CONTEXT, "id": 99}, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER) is not None
        
        # Cache should handle memory efficiently
        cache_stats = cache_manager.get_cache_stats()
        assert cache_stats is not None
    

    @pytest.mark.asyncio
    async def test_cache_error_handling(self, cache_manager):
        """Test cache error handling."""
        # Test with None/empty context (should be handled gracefully by _generate_cache_key)
        result = await cache_manager.get({}, {}, "")
        assert result is None
        
        # Test with empty context
        result = await cache_manager.get({}, {}, "")
        assert result is None
        
        # Test setting None value
        await cache_manager.set(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER, None)
        result = await cache_manager.get(DUMMY_PRODUCT_CONTEXT, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_cache_performance_monitoring(self, cache_manager):
        """Test cache performance monitoring."""
        # Perform various cache operations
        for i in range(10):
            product_context = {**DUMMY_PRODUCT_CONTEXT, "iteration": i}
            await cache_manager.set(product_context, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER, {"data": i})
            await cache_manager.get(product_context, DUMMY_GENERATION_PARAMS, DUMMY_PROVIDER)
        
        # Get performance stats
        stats = cache_manager.get_cache_stats()
        
        assert isinstance(stats, dict)
        assert "local_cache" in stats
        assert "total_entries" in stats["local_cache"]
    
    def test_cache_manager_initialization(self, cache_manager):
        """Test cache manager initialization."""
        assert cache_manager is not None
        assert hasattr(cache_manager, 'set')
        assert hasattr(cache_manager, 'get')
        assert hasattr(cache_manager, 'invalidate_by_product')
        assert hasattr(cache_manager, 'invalidate_by_provider')
        assert hasattr(cache_manager, 'warm_cache')
        assert hasattr(cache_manager, 'get_cache_stats')
